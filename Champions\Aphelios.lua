local Aphelios = {
	laste = 0,
	flrsar = 50,
	phqsar = 50,
	jjqsar = 50,
	sqsar = 50,
	zlpsar = 50,
	ApheliosCalibrumAttackOverride = 0,
	guncontrol_time = 0,
	BaseWindUp = 0.3, -- 添加默认的BaseWindUp值
	attack_time = 0, -- 添加attack_time变量来替代orbwallker.attack_time
	flash_slot = nil, -- 闪现技能槽位
	last_flash_check = 0, -- 上次检查闪现的时间
	-- Q闪相关变量
	qFlashActive = false,
	qFlashStartTime = 0,
	flashTimerActive = false,
	savedTarget = nil,
	flashScheduledTime = 0
}
local spell_data_lib = module.load(header.id, "spell/data")

local orb = module.internal("orb")
local preds = module.internal("pred")
local evade = module.seek("evade") or module.seek("evade_old")
local TS = module.internal('TS')
local AsUtility = module.load(header.id, "Libdata/Utility")
local AsVerification = module.load(header.id, "Libdata/lib_Verification")
local AsEvadeSpell = module.load(header.id, "Libdata/Spell")
local iswxhl,chinese = AsUtility.iswxhl(),AsUtility.ischinese()

-- 延迟执行函数
local delayedActions, delayedActionsExecuter = {}, nil
local function delayAction(func, delay, args)
	if not delayedActionsExecuter then
		function delayedActionsExecuter()
			for t, funcs in pairs(delayedActions) do
				if t <= os.clock() then
					for i = 1, #funcs do
						local f = funcs[i]
						if f and f.func then
							f.func(unpack(f.args or {}))
						end
					end
					delayedActions[t] = nil
				end
			end
		end
		cb.add(cb.tick, delayedActionsExecuter)
	end
	local t = os.clock() + (delay or 0)
	if delayedActions[t] then
		delayedActions[t][#delayedActions[t] + 1] = {func = func, args = args}
	else
		delayedActions[t] = {{func = func, args = args}}
	end
end
--local orbwallker = module.load(header.id, "orbwallker/orbwallker")
--local AntiGapcloser = module.load(header.id, "Lib/AntiGapcloser")
--AntiGapcloser:__init()
local QMANA,WMANA,EMANA,RMANA = 0,0,0,0

-- 获取闪现技能槽位
local function get_flash_slot()
	if player:spellSlot(4).name:lower():find("flash") then
		return 4
	elseif player:spellSlot(5).name:lower():find("flash") then
		return 5
	else
		return nil
	end
end

-- 检查闪现是否可用
local function can_flash()
	local flash_slot = get_flash_slot()
	if not flash_slot then return false end
	local flash_spell = player:spellSlot(flash_slot)
	return flash_spell and flash_spell.state == 0
end
Ignite = spell_data_lib['Ignite']
local ZLPQ = {slot = player:spellSlot(_Q),range = math.huge,delay = 0.3,speed = math.huge,width = math.huge,boundingRadiusMod = 1,collision = { hero = false, minion = false, wall = false}}
local PHQQ = {slot = player:spellSlot(_Q),range = 850,delay = 0.4,speed = 3400,width = 100,boundingRadiusMod = 1,collision = { hero = false, minion = false, wall = true}}
local JJQQ = {slot = player:spellSlot(_Q),range = 1450,delay = 0.40,speed = 1850,width = 60,boundingRadiusMod = 1,collision = { hero = true, minion = true, wall = true}}
local SQQ =  {slot = player:spellSlot(_Q),range = 550,delay = 0.00,speed = math.huge,width = math.huge,boundingRadiusMod = 1,collision = { hero = true, minion = true, wall = true}}
local FLRQ = {slot = player:spellSlot(_Q),range = 475,delay = 0.25,speed = math.huge,width = 500,boundingRadiusMod = 1,collision = { hero = true, minion = true, wall = true}}
local R = {slot = player:spellSlot(_R),range = 1300,delay = 0.6,speed = 2050,width = 125,boundingRadiusMod = 1,collision = { hero = true, minion = false, wall = true}}
local Last = {zlp = 0;sq = 0;phq = 0;flr = 0;jjq = 0;}
local Q = {}
ZLPQ.damage = function(target)
	if not target then return 0 end
	local source = player
	local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
	local total_z = (50 + 13.33 * j) + (AsUtility.GetBonusAD(source) * (0.26 + 0.05 * j))
	local returndmg_z = AsUtility.CalculateMagicDamage(target,total_z,source)
	
	return returndmg_z
end
PHQQ.damage = function(target)
	if not target then return 0 end
	local source = player
	local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
	local total_p = (25 + 6.6666 * j ) +(AsUtility.GetBonusAD(source) * (0.56 + 0.04 * j ))+ (AsUtility.GetTotalAP(source)*.7)
	local returndmg_p = AsUtility.CalculatePhysicalDamage(target,total_p,source) + AsUtility.CalculateAADamage(target) 
	if Aphelios.arms2 == "jjq" then
		returndmg_p = returndmg_p + AsUtility.CalculateAADamage(target) 
	end
	
	if Aphelios.next_is_action() then
		if Aphelios.arms2 == "zlp" then
			returndmg_p = returndmg_p+ZLPQ.damage(target)
		end
	end
	
	
	return returndmg_p
end
JJQQ.damage = function(target)
	if not target then return 0 end
	local source = player
	local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
	local total = (60 + 16.6666*j) + (AsUtility.GetBonusAD(source) * (0.42 + 0.03*j)) + (AsUtility.GetTotalAP(source))
	--副武器被动
	local Fu_pass = 15 + AsUtility.GetBonusAD(source) * .2
	local returndmg = AsUtility.CalculatePhysicalDamage(target,total+Fu_pass,source) + AsUtility.CalculateAADamage(target) 
	
	return returndmg
end
SQQ.damage = function(target)
	if not target then return 0 end
	local source = player
	local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
	local attackSpeedMod = ((source.attackSpeedMod - 1) * 100)
	local attackNum = 6 + math.floor(attackSpeedMod/100 / 0.5)  -- 更新为正确的计算公式
	local total_red = (10 + 3.3333 * j )+(AsUtility.GetBonusAD(source) * (0.21 + 0.015 * j)) * attackNum
	local returndmg_red = AsUtility.CalculatePhysicalDamage(target,total_red,source)	
	if Aphelios.arms2 == "jjq" then
		returndmg_red = returndmg_red + AsUtility.CalculateAADamage(target) 
	end
	return returndmg_red
end
FLRQ.damage = function(target)
	if not target then return 0 end
	local source = player
	local j = math.min(7,math.max(1,math.ceil(player.levelRef / 2 ))) - 1
	local total_f = (25+10*j) + (AsUtility.GetBonusAD(source) * (0.35+0.025*j)) + (AsUtility.GetTotalAP(source)*.5)
	local returndmg_p = AsUtility.CalculatePhysicalDamage(target,total_f,source)
	if Aphelios.arms2 == "jjq" then
		returndmg_p = returndmg_p + AsUtility.CalculateAADamage(target) 
	end
	return returndmg_p
end

R.damage = function(target,name,source)
	if not target or not name  then return 0 end
	local source = source or player
	local spell_tb = {125,175,225}
	local spell_tb_b = {50,80,100}
	local spell_level = source:spellSlot(_R).level
	
	local spell_Dmg = spell_tb[spell_level] or 0;
	local spell_Dmg_b = spell_tb_b[spell_level] or 0;
	local exdamag = 0
	local totalDmg = spell_Dmg + (AsUtility.GetBonusAD(source) * .20) + (AsUtility.GetTotalAP(source)*1);
	if  name == "jjq" then
		totalDmg = totalDmg+spell_Dmg_b
		local can_cal = false
		if Aphelios.arms1 == "jjq" then
			if AsUtility.isq() then
				can_cal = true
			end
		elseif Aphelios.arms2 == "jjq" then
			if Aphelios.next_is_action() then
				can_cal = true
			end
		end
		if can_cal then
			local seg = preds.linear.get_prediction(JJQQ, target)
			if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,target) and  (Aphelios:trace_filter(JJQQ,seg,target) or AsUtility.iskq()) then
				exdamag = JJQQ.damage(target)
			end
		end
		--被动
		local Fu_pass = 15 + AsUtility.GetBonusAD(source) * .2
		local returndmg = AsUtility.CalculatePhysicalDamage(target,Fu_pass,source) + AsUtility.CalculateAADamage(target) 
		exdamag = exdamag+returndmg
	end
	local returndmg = AsUtility.CalculatePhysicalDamage(target,totalDmg,source) - AsUtility.jshfhp(target,1)
	if name == "zlp" then--R+AA+附带重力炮BUFF + 重力炮伤害 可以计算范围多加1个AA伤害 坠明
		return returndmg + AsUtility.CalculateAADamage(target, source) * 2 + ZLPQ.damage(target)
	elseif name == "sq" then
		return returndmg + AsUtility.CalculateAADamage(target, source)
	elseif name == "flr" then
		return returndmg + AsUtility.CalculateAADamage(target, source)
	elseif name == "jjq" then
		return returndmg + AsUtility.CalculateAADamage(target, source) + exdamag
	elseif name == "phq" then
		return returndmg + AsUtility.CalculateAADamage(target, source) * 1.9
	end
end


if AsVerification  and AsVerification.IsSuccess then
	if chinese then
		Aphelios.menu = menu("[Brian] Aphelios" ,  AsVerification.name .." 残月之肃");
		Aphelios.menu:menu("q", "Q 技能设置")
		Aphelios.menu.q:menu("sq", "断魄")
		Aphelios.menu.q.sq:boolean("combo","连招使用", true)
		
		Aphelios.menu.q:menu("flr", "折镜")
		Aphelios.menu.q.flr:boolean("combo","连招使用", true)
		Aphelios.menu.q.flr:slider('saves', "保留层数", 15, 1, 50, 1)
		
		Aphelios.menu.q.flr:slider('use', "使用层数", 5, 1, 50, 1)
		Aphelios.menu.q.flr.use:set(
			"tooltip",
			"飞碟必须大于X层数才会强制使用"
		)
		
		Aphelios.menu.q:menu("jjq", "通碧")
		Aphelios.menu.q.jjq:boolean("combo","连招使用", true)
		Aphelios.menu.q.jjq:slider('saves', "保留层数", 10, 1, 50, 1)
		Aphelios.menu.q.jjq:keybind("qflash","通碧Q闪", "G", nil)
		Aphelios.menu.q.jjq:slider('qflash_range', "Q闪搜索范围", 850, 600, 1200, 50)
		Aphelios.menu.q.jjq:slider('qflash_hp', "最低血量百分比", 30, 10, 80, 5)
		
		Aphelios.menu.q:menu("zlp", "坠明")
		Aphelios.menu.q.zlp:boolean("combo","连招使用", true)
		Aphelios.menu.q.zlp:keybind("zlpkey", "重力炮快捷键", "A", nil)
		
		
		Aphelios.menu.q:menu("phq", "莹焰")
		Aphelios.menu.q.phq:boolean("combo","连招使用", true)
		
		
		Aphelios.menu:menu("w", "W 技能设置")
		Aphelios.menu.w:boolean("combo","连招使用W", true)
		Aphelios.menu.w:keybind("closeguncontrol", "暂停切枪按键", "W", nil)
		Aphelios.menu.w:slider('closetime', "暂停时间(毫秒)", 2000, 0, 5000, 50)
		
		
		Aphelios.menu:menu("r", "R 技能设置")
		Aphelios.menu.r:slider('userange', "使用范围", 1300, 1000, 1300, 10)
		Aphelios.menu.r:boolean("kill","击杀使用", true)
		Aphelios.menu.r:header("kil1l","折镜")
		Aphelios.menu.r:boolean("flrcombo","AA范围内折镜R使用", true)
		
		Aphelios.menu.r:header("kil1l","通碧")
		Aphelios.menu.r:menu("jjqmenu", "通碧设置")
		Aphelios.menu.r.jjqmenu:boolean("jjqaacombo","AA范围内通碧R使用", true)
		Aphelios.menu.r.jjqmenu:boolean("jjqcombo","通碧+坠明使用", true)
		Aphelios.menu.r.jjqmenu:slider('jjqhit', "命中数", 3, 1, 5, 1)
		Aphelios.menu.r.jjqmenu:menu("list", "使用列表")
		for i=0, objManager.enemies_n-1 do
			local obj = objManager.enemies[i]
			Aphelios.menu.r.jjqmenu.list:boolean("on" .. obj.charName, obj.charName, true)
		end
		
		Aphelios.menu.r:header("sq_kil1l"," 断魄")
		Aphelios.menu.r:boolean("sqcombo","断魄使用", true)
		Aphelios.menu.r:slider('sqhit', "低于血量", 25, 1, 100, 1)
		
		Aphelios.menu.r:header("kil1l"," 坠明")
		Aphelios.menu.r:boolean("zlpcombo","坠明使用", true)
		Aphelios.menu.r:slider('zlphit', "命中数", 3, 1, 5, 1)
		
		Aphelios.menu.r:header("kil1l","莹焰")
		Aphelios.menu.r:boolean("phqcombo","莹焰使用", true)
		Aphelios.menu.r:slider('phqhit', "命中数", 3, 1, 5, 1)
		
		
		
		
		Aphelios.menu.r:header("kil1l","   ---   ")
		Aphelios.menu.r:keybind("manual", "人工智能 R", "T", nil)
		Aphelios.menu.r:dropdown("mode", "目标选择模式", 7, {"AD/AP优先", "自动", "最低AD伤害", "最低AP伤害", "最低血量", "离我最近", "离鼠标最近","最快杀死"})
		
		
		
		
		Aphelios.menu:menu("guncontrol", "控枪")
		Aphelios.menu.guncontrol:boolean("open","启动", true)
		Aphelios.menu.guncontrol:boolean("evade_not","躲避禁止使用", true)
		
		Aphelios.menu.guncontrol:slider("level_not", "小于等于X等级禁止切枪", 1, 0, 18, 1)
		Aphelios.menu.guncontrol:slider("checkaa", "X下AA伤害杀死暂时关闭", 3, 0, 10, 1)
		Aphelios.menu.guncontrol:header("kqheader","断魄-通碧 用掉断魂")
		Aphelios.menu.guncontrol:header("kqheader","断魄-折镜 用掉断魄")
		Aphelios.menu.guncontrol:header("kqheader","断魄-莹焰 用掉莹焰")
		Aphelios.menu.guncontrol:header("kqheader","断魄-坠明 用掉断魄")
		Aphelios.menu.guncontrol:header("kqheader","莹焰-坠明 用掉坠明")
		Aphelios.menu.guncontrol:header("kqheader","通碧-坠明 用掉通碧")
		Aphelios.menu.guncontrol:header("kqheader","通碧-折镜 用掉折镜")
		Aphelios.menu.guncontrol:header("kqheader","折镜-莹焰 用掉莹焰")
		Aphelios.menu.guncontrol:header("kqheader","折镜-通碧 用掉折镜")
		
		
		Aphelios.menu:menu("keys", "按键设置")
		Aphelios.menu.keys:header("kil1l","按键设置")
		Aphelios.menu.keys:keybind("combokey", "连招", "Space", nil)
		Aphelios.menu.keys:keybind("harasskey", "骚扰", "C", nil)
		Aphelios.menu.keys:keybind("clearkey", "清线/清野", "V", nil)
		Aphelios.menu.keys:keybind("lastkey", "最后一击", "X", nil)
	else
		Aphelios.menu = menu("[Brian] Aphelios" ,  AsVerification.name .." Aphelios");
		Aphelios.menu:menu("q", "Q Spell Settings")
		Aphelios.menu.q:menu("sq", "Scythe Pistol")
		Aphelios.menu.q.sq:boolean("combo","Combo Use", true)
		
		Aphelios.menu.q:menu("flr", "Chakram")
		Aphelios.menu.q.flr:boolean("combo","Combo Use", true)
		Aphelios.menu.q.flr:slider('saves', "Keep the stack", 15, 1, 50, 1)
		
		Aphelios.menu.q.flr:slider('use', "Use the stack", 5, 1, 50, 1)
		Aphelios.menu.q.flr.use:set(
			"tooltip",
			"Chakram must be larger than the X stack to be mandatory"
		)
		
		Aphelios.menu.q:menu("jjq", "Sniper Rifle")
		Aphelios.menu.q.jjq:boolean("combo","Combo Use", true)
		Aphelios.menu.q.jjq:slider('saves', "Keep the stack", 10, 1, 50, 1)
		Aphelios.menu.q.jjq:keybind("qflash","Q Flash", "G", nil)
		Aphelios.menu.q.jjq:slider('qflash_range', "Q Flash Search Range", 850, 600, 1200, 50)
		Aphelios.menu.q.jjq:slider('qflash_hp', "Min HP Percent", 30, 10, 80, 5)
		
		Aphelios.menu.q:menu("zlp", "Gravity Cannon")
		Aphelios.menu.q.zlp:boolean("combo","Combo Use", true)
		Aphelios.menu.q.zlp:keybind("zlpkey", "Gravity Key", "A", nil)
		
		Aphelios.menu.q:menu("phq", "Flamethrower")
		Aphelios.menu.q.phq:boolean("combo","Combo Use", true)
		
		
		Aphelios.menu:menu("w", "W Spell Settings")
		Aphelios.menu.w:boolean("combo","Combo Use", true)
		Aphelios.menu.w:keybind("closeguncontrol", "Pause cutting gun button", "W", nil)
		Aphelios.menu.w:slider('closetime', "Pause Time(MS)", 2000, 0, 5000, 50)
		Aphelios.menu:menu("r", "R Spell Settings")
		Aphelios.menu.r:slider('userange', "Use Range", 1300, 1000, 1300, 10)
		Aphelios.menu.r:boolean("kill","Kill Use", true)
		Aphelios.menu.r:header("kil1l","Chakram")
		Aphelios.menu.r:boolean("flrcombo","Chakram R use in the AA range", true)
		
		Aphelios.menu.r:header("kil1l","Rifle")
		
		Aphelios.menu.r:menu("jjqmenu", "Rifle Settings")
		Aphelios.menu.r.jjqmenu:boolean("jjqaacombo","Rifle AA Range Use", true)
		Aphelios.menu.r.jjqmenu:boolean("jjqcombo","Sniper Rifle+Gravity Cannon Use", true)
		Aphelios.menu.r.jjqmenu:slider('jjqhit', "Hit", 3, 1, 5, 1)
		Aphelios.menu.r.jjqmenu:menu("list", "List")
		for i=0, objManager.enemies_n-1 do
			local obj = objManager.enemies[i]
			Aphelios.menu.r.jjqmenu.list:boolean("on" .. obj.charName, obj.charName, true)
		end
		
		
		
		Aphelios.menu.r:header("sq_kil1l"," Scythe Pistol")
		Aphelios.menu.r:boolean("sqcombo","Scythe Pistol Use", true)
		Aphelios.menu.r:slider('sqhit', "LOW HP", 25, 1, 100, 1)
		
		Aphelios.menu.r:header("kil1l"," Gravity Cannon")
		Aphelios.menu.r:boolean("zlpcombo","Gravity Cannon Use", true)
		Aphelios.menu.r:slider('zlphit', "Hit", 3, 1, 5, 1)
		
		Aphelios.menu.r:header("kil1l","Flamethrower")
		Aphelios.menu.r:boolean("phqcombo","Flamethrower Use", true)
		Aphelios.menu.r:slider('phqhit', "Hit", 3, 1, 5, 1)
		
		
		
		Aphelios.menu.r:header("kil1l","   ---   ")
		Aphelios.menu.r:keybind("manual", "manual R", "T", nil)
		Aphelios.menu.r:dropdown("mode", "Target selection mode", 7, {"AD/AP", "Auto", "Min AD", "Min AP", "Min HP", "Nearest to me", "Nearest to Mouse","Fastest kill"})
		
		
		
		
		Aphelios.menu:menu("guncontrol", "Gun Control")
		Aphelios.menu.guncontrol:boolean("open","open", true)
		
		Aphelios.menu.guncontrol:slider("level_not", "<=X Level closed", 1, 0, 18, 1)
		Aphelios.menu.guncontrol:slider("checkaa", "AA damage kill under X is temporarily closed", 3, 0, 10, 1)
		Aphelios.menu.guncontrol:boolean("evade_not","evade close Use", true)
		
		
		Aphelios.menu:menu("keys", "Key Settings")
		Aphelios.menu.keys:keybind("combokey", "Combo", "Space", nil)
		Aphelios.menu.keys:keybind("harasskey", "Haras", "C", nil)
		Aphelios.menu.keys:keybind("clearkey", "Clear", "V", nil)
		Aphelios.menu.keys:keybind("lastkey", "Last", "X", nil)
		
	end
	Aphelios.menu:boolean("dev","Developer mode", false)
	local sadebug = function(msg)
		-- if Aphelios.menu.dev:get() then
			-- chat.clear()
			-- chat.add("[SA Debug]: " .. tostring(msg), {color = '#ff0084', bold = true, italic = true})
			-- chat.print()
			-- print("[SA Debug]: " .. tostring(msg))
		-- end
	end
	--AsUtility.seticon(Aphelios.menu)
	
	function Aphelios:SetMana()
		QMANA = ZLPQ.slot.level>0 and player.manaCost0 or 0
		RMANA = R.slot.level>0 and player.manaCost3 or 0
	end
	--ApheliosOffHandBuffGravitum 重力炮
	--ApheliosOffHandBuffInfernum 喷火器
	--ApheliosOffHandBuffSeverum 手枪
	--ApheliosOffHandBuffCalibrum 狙击枪
	--ApheliosOffHandBuffCrescendum 飞轮刃
	function Aphelios:get_arms()
		local Arms1 = ''
		local Arms2 = ''
		if ZLPQ.slot.name == 'ApheliosGravitumQ' then
			Arms1 = 'zlp'
		elseif  ZLPQ.slot.name == 'ApheliosInfernumQ' then
			Arms1 = 'phq'
		elseif  ZLPQ.slot.name == 'ApheliosCrescendumQ' then
			Arms1 = 'flr'
		elseif  ZLPQ.slot.name == 'ApheliosCalibrumQ' then
			Arms1 = 'jjq'
		elseif  ZLPQ.slot.name == 'ApheliosSeverumQ' then
			Arms1 = 'sq'
		end
		if player.buff['apheliosoffhandbuffinfernum'] then
			Arms2 = 'phq'
		elseif player.buff['apheliosoffhandbuffgravitum'] then
			Arms2 = 'zlp'
		elseif player.buff['apheliosoffhandbuffseverum'] then
			Arms2 = 'sq'
		elseif player.buff['apheliosoffhandbuffcalibrum'] then
			Arms2 = 'jjq'
		elseif player.buff['apheliosoffhandbuffcrescendum'] then
			Arms2 = 'flr'
		end
		return Arms1,Arms2
	end
	local switch = 0
	function Aphelios:init()
		--[[
		if Target then
			AsUtility.testbuff(Target)
		end--]]
		Aphelios:SetMana()
		local Arms1,Arms2 = Aphelios:get_arms()
		Aphelios.arms1 = Arms1
		Aphelios.arms2 = Arms2
		Aphelios.next_time = orb.core.time_to_next_attack()
		R.range = Aphelios.menu.r.userange:get()

		-- 定期检查闪现槽位
		if os.clock() - Aphelios.last_flash_check > 5 then
			Aphelios.flash_slot = get_flash_slot()
			Aphelios.last_flash_check = os.clock()
		end
		if Aphelios.ApheliosCalibrumAttackOverride and Aphelios.ApheliosCalibrumAttackOverride > os.clock() then
			
			--player:move(mousePos)
		end
		if orb.core.can_attack() then
			if (Aphelios.menu.keys.combokey:get() or Aphelios.menu.keys.harasskey:get()) then
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) and enemy.buff['aphelioscalibrumbonusrangedebuff'] and enemy.pos:dist(player.pos) < 1800 then
						orb.combat.target = nil
						player:attack(enemy)
						enemy.buff['aphelioscalibrumbonusrangedebuff'] = nil
						orb.core.set_server_pause()
						return
					end
				end
			elseif Aphelios.menu.keys.clearkey:get() then
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) and enemy.buff['aphelioscalibrumbonusrangedebuff'] and enemy.pos:dist(player.pos) < 1800 then
						orb.combat.target = nil
						player:attack(enemy)
						enemy.buff['aphelioscalibrumbonusrangedebuff'] = nil
						orb.core.set_server_pause()
						return
					end
				end
				for i=0, objManager.minions.size[TEAM_ENEMY]-1 do
					local obj = objManager.minions[TEAM_ENEMY][i]
					if obj and not obj.isDead and obj.isTargetable and obj.isVisible and obj.buff['aphelioscalibrumbonusrangedebuff']  then
						if player.path.serverPos2D:distSqr(obj.path.serverPos2D) < 1800*1800 then
							orb.combat.target = nil
							player:attack(obj)
							obj.buff['aphelioscalibrumbonusrangedebuff'] = nil
							orb.core.set_server_pause()
							return
						end
					end
				end
				for i=0, objManager.minions.size[TEAM_NEUTRAL]-1 do
					local obj = objManager.minions[TEAM_NEUTRAL][i]
					if obj and not obj.isDead and obj.isTargetable and obj.isVisible and obj.buff['aphelioscalibrumbonusrangedebuff']  then
						if player.path.serverPos2D:distSqr(obj.path.serverPos2D) < 1800*1800 then
							orb.combat.target = nil
							player:attack(obj)
							obj.buff['aphelioscalibrumbonusrangedebuff'] = nil
							orb.core.set_server_pause()
							return
						end
					end
				end
			end
			
			
		end
		
		
		--快速重力炮使用
		if Aphelios.menu.q.zlp.combo:get() and Aphelios.ZLPQTARGET and Aphelios.ZLPQTARGET > os.clock() and AsUtility.isq() and ZLPQ.slot.name == 'ApheliosGravitumQ' and orb.core.can_action() then
			player:castSpell("self",0)
			orb.core.set_server_pause()
			return true
		end
		
		if player.buff['apheliosseverumq'] or player.buff['apheliospreload'] then
			orb.core.set_pause_attack(math.huge)
			if player.buff['apheliosseverumq'] then
				player:move(mousePos)
			end
		elseif orb.core.is_attack_paused() then
			orb.core.set_pause_attack(0)
		end
		if Arms1 == "phq"  then
			Q = PHQQ
		elseif Arms1 == "zlp" then
			Q = ZLPQ
		elseif Arms1 == "flr" then
			Q = FLRQ
		elseif Arms1 == "jjq" then
			Q = JJQQ
		elseif Arms1 == "sq" then
			Q = SQQ
		end
		if ZLPQ.slot.cooldown > 0 then
			if Arms1 == "phq"  then
				Last.phq = game.time + ZLPQ.slot.cooldown
			elseif Arms1 == "zlp" then
				Last.zlp = game.time + ZLPQ.slot.cooldown
			elseif Arms1 == "flr" then
				Last.flr = game.time + ZLPQ.slot.cooldown
			elseif Arms1 == "jjq" then
				Last.jjq = game.time + ZLPQ.slot.cooldown
			elseif Arms1 == "sq" then
				Last.sq = game.time + ZLPQ.slot.cooldown
			end
			
			
		end
		
		if Aphelios.arms1 ~= "phq" and Aphelios.arms2 ~= "phq" then
			Aphelios.phqsar = 50
		elseif Aphelios.arms1 == "phq" then
			Aphelios.phqsar = player.sar
		end
		if Aphelios.arms1 ~= "flr" and Aphelios.arms2 ~= "flr" then
			Aphelios.flrsar = 50
		elseif Aphelios.arms1 == "flr" then
			Aphelios.flrsar = player.sar
		end
		if Aphelios.arms1 ~= "sq" and Aphelios.arms2 ~= "sq" then
			Aphelios.sqsar = 50
		elseif Aphelios.arms1 == "sq" then
			Aphelios.sqsar = player.sar
		end
		if Aphelios.arms1 ~= "zlp" and Aphelios.arms2 ~= "zlp" then
			Aphelios.zlpsar = 50
		elseif Aphelios.arms1 == "zlp" then
			Aphelios.zlpsar = player.sar
		end
		if Aphelios.arms1 ~= "jjq" and Aphelios.arms2 ~= "jjq" then
			Aphelios.jjqsar = 50
		elseif Aphelios.arms1 == "jjq" then
			Aphelios.jjqsar = player.sar
		end
		
		if Aphelios.menu.guncontrol.open:get() then
			if Aphelios.menu.w.closeguncontrol:get() then
				if os.clock() > switch then
					Aphelios.guncontrol_time = os.clock() + (Aphelios.menu.w.closetime:get() / 1000)
					switch = os.clock() + 0.25
				end
			end
		end 
		
		
	end
	
	
	function Aphelios:next_is_action()
		
		local flat = false
		local msg = ""
		local Arms2 = Aphelios.arms2
		local range = 0
		if Arms2 == "phq"  then
			if game.time - Last.phq >= 0 then
				flat = true
				msg = "phq"
				range = PHQQ.range
			end
		elseif Arms2 == "zlp" then
			if game.time - Last.zlp >= 0 then
				flat = true
				msg = "zlp"
				range = ZLPQ.range
			end
		elseif Arms2 == "flr" then
			if game.time - Last.flr >= 0 then
				flat = true
				msg = "flr"
				range = FLRQ.range
			end
		elseif Arms2 == "jjq" then
			if game.time - Last.jjq >= 0 then
				flat = true
				msg = "jjq"
				range = JJQQ.range
			end
		elseif Arms2 == "sq" then
			if game.time - Last.sq >= 0 then
				flat = true
				msg = "sq"
				range = SQQ.range
			end
		end
		if player.levelRef  < 2 then 
			flat = false 
		end
		return flat,msg,range
	end
	
	function Aphelios:check_safe(point)
		local pos = player.pos - (point - player.pos):norm() * 390;
		if AsUtility.UnderTurret(pos) then return false end
		if (evade and not evade.core.is_action_safe(pos, 1600, 0)) then return false end
		if #AsUtility.CountEnemiesInRange(pos,500) > #AsUtility.CountEnemiesInRange(player.pos,500) then return false end 
		return true
	end
	
	function Aphelios:trace_filter(input,segment, target)
		
		if preds.trace.linear.hardlock(input, segment, target) then
			return true
		end
		if preds.trace.linear.hardlockmove(input, segment, target) then
			return true
		end
		local d1 = segment.startPos:dist(segment.endPos)		
		local d2 = input.range
		local d3 = segment.startPos:dist(target.path.serverPos2D)
		
		if d1 < 500 then
			return true
		end
		
		if preds.trace.newpath(target, 0.033, 0.250) then
			return true
		end
	end
	function Aphelios:trace_filter_c(input,segment, target)
		if preds.trace.circular.hardlock(input, segment, target) then
			return true
		end
		if preds.trace.circular.hardlockmove(input, segment, target) then
			return true
		end
		local d1 = segment.startPos:dist(segment.endPos)
		local d2 = input.range
		local d3 = segment.startPos:dist(target.path.serverPos2D)
		if d1 > (d2 + 25) then
			return false
		end
		if d2 + 25 < d3 then
			return false
		end
		if d1 < 400 then
			return true
		end
		if preds.trace.newpath(target, 0.033, 0.500) then
			return true
		end
	end
	
	
	function Aphelios:loincR()
		if orb.core.can_action()  then
			
			if ((Aphelios.arms1  == "jjq" and AsUtility.isq()) or  (Aphelios.arms2 == "jjq" and Aphelios.next_is_action())  )then
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) and enemy.pos:dist(player) < JJQQ.range then
						local hp = orb.farm.predict_hp(enemy, 1) + enemy.allShield
						if  hp and JJQQ.damage(enemy) > hp then
							local seg = preds.linear.get_prediction(JJQQ,enemy)
							if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,enemy) and (Aphelios:trace_filter(JJQQ,seg,enemy) or AsUtility.iskq()) then
								if Aphelios.arms2 == "jjq" then
									player:castSpell("self",1)
									Aphelios.guncontrol_time = os.clock() + 1
								else
									player:castSpell("pos",0,seg.endPos:to3D(mousePos.y))
									orb.core.set_server_pause()
								end
								return true
							end
						end
					end
				end
			
			end
			
			if Aphelios.menu.r.kill:get() and AsUtility.isr() then
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) then
						local Can_Use = true
						if Aphelios.arms1  == "jjq" then
							if not Aphelios.menu.r.jjqmenu.jjqaacombo:get() and enemy.pos:dist(player.pos) <= AsUtility.GetAARange(enemy) then
								Can_Use = false
							end
							if Aphelios.menu.r.jjqmenu.list["on" .. enemy.charName] and Aphelios.menu.r.jjqmenu.list["on" .. enemy.charName]:get() then
							
							else
								Can_Use = false
							end
						elseif Aphelios.arms1 == "flr" then
							if not Aphelios.menu.r.flrcombo:get() and enemy.pos:dist(player.pos) <= AsUtility.GetAARange(enemy) then
								Can_Use = false
							end
						end
						
						if Can_Use then
							local rdamage = R.damage(enemy,Aphelios.arms1)
							local hp = orb.farm.predict_hp(enemy, 1) + enemy.allShield
							local cal_hp = 0
							if enemy.pos:dist(player) <= AsUtility.GetAARange(enemy) then
								cal_hp = AsUtility.CalculateAADamage(enemy, player)
							end
							if hp and hp > cal_hp then
								if rdamage > hp  then
									if enemy.pos:dist(player) <= AsUtility.GetAARange(enemy) then
										if hp > AsUtility.CalculateAADamage(enemy) then
											local seg = preds.linear.get_prediction(R, enemy)
											if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
												player:castSpell("pos", 3, seg.endPos:to3D())
												--print("cast1")
												orb.core.set_server_pause()
												return
											end
										end
									elseif hp > 0 then
										local seg = preds.linear.get_prediction(R, enemy)
										if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
											player:castSpell("pos", 3, seg.endPos:to3D())
											--print("cast2")
											orb.core.set_server_pause()
											return
										end
									end
								else
									local rdamage2 = R.damage(enemy,Aphelios.arms2)
									if rdamage2 > hp  then
										if enemy.pos:dist(player) <= AsUtility.GetAARange(enemy) then
										if hp > AsUtility.CalculateAADamage(enemy) then
											local seg = preds.linear.get_prediction(R, enemy)
											if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
												player:castSpell("self", 1)
												Aphelios.guncontrol_time = os.clock() + 1
												--print("castwr1")
												return
											end
										end
									elseif hp > 0 then
										local seg = preds.linear.get_prediction(R, enemy)
										if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
											player:castSpell("self", 1)
											Aphelios.guncontrol_time = os.clock() + 1
											print("castwr2")
											return
										end
									end
									
									end
								end
							end
							
						
						end
						
					end
				end
				
				
			end
			
			if Aphelios.menu.r.phqcombo:get() and (Aphelios.arms1 == "phq" or Aphelios.arms2 == "phq") and AsUtility.isr()  then --坠明 HIT使用
				if Aphelios.arms1  ~= "phq" and not AsUtility.isw() or not Aphelios:next_is_action() then
					return
				end
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) then
						local seg = preds.linear.get_prediction(R, enemy)
						if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
							
							if #AsUtility.count_enemies_in_range(seg.endPos:to3D(), 400) >= Aphelios.menu.r.phqhit:get() then
								if Aphelios.arms1  ~= "phq" then
									player:castSpell("self",1)
								end
								player:castSpell("pos", 3, seg.endPos:to3D())
								--print("cast5")
								orb.core.set_server_pause()
								return
							end
						end
					end
				end
				return
			end
			
			
			if Aphelios.menu.r.flrcombo:get() and AsUtility.isr() and Aphelios.arms1 == "flr" and orb.combat.target and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target) and Aphelios.flrsar > Aphelios.menu.q.flr.saves:get() and orb.farm.predict_hp(orb.combat.target, 1) > AsUtility.CalculateAADamage(orb.combat.target)*2 and orb.combat.target.pos:dist(player) < 400 then
				if AsUtility.isTarget(orb.combat.target,true) then
					local seg = preds.linear.get_prediction(R,orb.combat.target)
					if seg then
						--print("cast3")
						player:castSpell("pos",3,seg.endPos:to3D(mousePos.y))
						orb.core.set_server_pause()
						if not player.buff['aphelioscrescendumorbitmanager'] then
							player.buff['aphelioscrescendumorbitmanager'] = {stacks2 = 6}
						end
						return true
					end
				end
			end
			
			if Aphelios.menu.r.jjqmenu.jjqcombo:get() and AsUtility.isr() and (Aphelios.arms1 == "jjq" or Aphelios.arms2 == "jjq") and (Aphelios.arms1 == "zlp" or Aphelios.arms2 == "zlp")  then --狙击枪+坠明 HIT使用
				if AsUtility.isq() and Aphelios:next_is_action() then
					if Aphelios.arms1  ~= "jjq" and not AsUtility.isw() then
						return
					end
					for i=0, objManager.enemies_n - 1 do
						local enemy = objManager.enemies[i]
						if enemy and  AsUtility.isTarget(enemy) and Aphelios.menu.r.jjqmenu.list["on" .. enemy.charName] and Aphelios.menu.r.jjqmenu.list["on" .. enemy.charName]:get()  then
							if Aphelios.menu.r.jjqmenu.jjqaacombo:get() or enemy.pos:dist(player.pos) > AsUtility.GetAARange(enemy) then
								local seg = preds.linear.get_prediction(R, enemy)
								
								if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
									if #AsUtility.count_enemies_in_range(seg.endPos:to3D(), 400) >= Aphelios.menu.r.jjqmenu.jjqhit:get() then
										
										if Aphelios.arms1  ~= "jjq" then
											player:castSpell("self",1)
										end
										player:castSpell("pos", 3, seg.endPos:to3D())
										--print("cast4")
										orb.core.set_server_pause()
										return
									end
								end
							end
							
							
						end
					end
				end
				return
			end
			
			
			
			if Aphelios.menu.r.sqcombo:get() and (Aphelios.arms1 == "sq" or Aphelios.arms2 == "sq") and AsUtility.isr() and AsUtility.GetPercentHealth(player) <= Aphelios.menu.r.sqhit:get()  then --坠明 HIT使用
			
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) then
						local seg = preds.linear.get_prediction(R, enemy)
						if seg and seg:length() < R.range and  Aphelios:trace_filter(R,seg,enemy) then
							if Aphelios.arms1  ~= "sq" then
								player:castSpell("self",1)
							end
							player:castSpell("pos", 3, seg.endPos:to3D())
							--print("cast_sq")
							orb.core.set_server_pause()
							return
						end
					end
				end
			end
			
			
			if Aphelios.menu.r.zlpcombo:get() and (Aphelios.arms1 == "zlp" or Aphelios.arms2 == "zlp") and AsUtility.isr()  then --坠明 HIT使用
				
				if Aphelios.arms1  ~= "zlp" and not AsUtility.isw() or not Aphelios:next_is_action() then
					return
				end
				if Aphelios.arms1  == "zlp" and ZLPQ.slot.cooldown > 1 then
					return
				end
				
				for i=0, objManager.enemies_n - 1 do
					local enemy = objManager.enemies[i]
					if enemy and  AsUtility.isTarget(enemy) then
						local seg = preds.linear.get_prediction(R, enemy)
						if seg and seg:length() < R.range and not preds.collision.get_prediction(R,seg,enemy) and  Aphelios:trace_filter(R,seg,enemy) then
							
							if #AsUtility.count_enemies_in_range(seg.endPos:to3D(), 400) >= Aphelios.menu.r.zlphit:get() then
								if Aphelios.arms1  ~= "zlp" then
									player:castSpell("self",1)
								end
								player:castSpell("pos", 3, seg.endPos:to3D())
								--print("cast6")
								orb.core.set_server_pause()
								return
							end
						end
					end
				end
				return
			end
			
		
		end
	end
	
	function Aphelios:guncontrol(can_use)
		if Aphelios.menu.guncontrol.evade_not:get() and evade and evade.core.is_active() then return end
		if Aphelios.menu.guncontrol.level_not:get() >= player.levelRef then return end
		--if orbwallker.farm.lane_clear_wait() then return end
		if orb.combat.target then
			local checkaa = Aphelios.menu.guncontrol.checkaa:get()
			local checkDag =  (AsUtility.GetOrbAADageme(player,orb.combat.target))*checkaa
			if checkDag > orb.combat.target.health then
				return
			end
		end
		
		if Aphelios.guncontrol_time > os.clock() then return end
		if not orb.core.can_action() then return end
		local windUpTime = 1/(player.attackSpeedMod*Aphelios.BaseWindUp)
		
		
		if Aphelios.menu.guncontrol.open:get() and (Aphelios.menu.keys.clearkey:get() or can_use) and orb.core.can_action() and AsUtility.isw() and os.clock() > (Aphelios.attack_time + windUpTime) then
			if orb.combat.target and Aphelios.next_time < 0.15 then return end
			if (Aphelios.arms1 == "sq" and Aphelios.arms2 == "jjq") or (Aphelios.arms1 == "jjq" and Aphelios.arms2 == "sq") then  --断魄+通碧 用掉断魂
				if Aphelios.arms1 ~= "sq" then 
					player:castSpell("self",1)
					--print("断魄+通碧 用掉断魂")
					return true
				end
			elseif (Aphelios.arms1 == "zlp" and Aphelios.arms2 == "sq") or (Aphelios.arms1 == "sq" and Aphelios.arms2 == "zlp") then  --坠明+通碧 用掉通碧
				if Aphelios.arms1 ~= "sq" then 
					player:castSpell("self",1)
					--print("断魄+坠明 用掉断魄")
					return true
				end
			elseif (Aphelios.arms1 == "zlp" and Aphelios.arms2 == "jjq") or (Aphelios.arms1 == "jjq" and Aphelios.arms2 == "zlp") then  --坠明+通碧 用掉通碧
				if Aphelios.arms1 ~= "jjq" then 
					player:castSpell("self",1)
					--print("坠明+通碧 用掉通碧")
					return true
				end
			elseif (Aphelios.arms1 == "phq" and Aphelios.arms2 == "zlp") or (Aphelios.arms1 == "zlp" and Aphelios.arms2 == "phq") then  --莹焰+坠明 用掉坠明
				if Aphelios.arms1 ~= "zlp" then 
					player:castSpell("self",1)
					--print("莹焰+坠明 用掉坠明")
					return true
				end
			elseif (Aphelios.arms1 == "sq" and Aphelios.arms2 == "flr") or (Aphelios.arms1 == "flr" and Aphelios.arms2 == "sq") then  --断魄+折镜 用掉断魄
				if Aphelios.arms1 ~= "sq" then 
					player:castSpell("self",1)
					--print("断魄+折镜 用掉断魄")
					return true
				end
			elseif (Aphelios.arms1 == "jjq" and Aphelios.arms2 == "flr") or (Aphelios.arms1 == "flr" and Aphelios.arms2 == "jjq") then  --通碧+折镜 用掉折镜
				if Aphelios.arms1 ~= "flr" then 
					player:castSpell("self",1)
					--print("通碧+折镜 用掉折镜")
					return true
				end
			elseif (Aphelios.arms1 == "jjq" and Aphelios.arms2 == "zlp") or (Aphelios.arms1 == "zlp" and Aphelios.arms2 == "jjq") then  --通碧+坠明 用掉通碧
				if Aphelios.arms1 ~= "jjq" then 
					player:castSpell("self",1)
					--print("通碧+坠明 用掉通碧")
					return true
				end
			elseif (Aphelios.arms1 == "sq" and Aphelios.arms2 == "phq") or (Aphelios.arms1 == "phq" and Aphelios.arms2 == "sq") then  --断魄+莹焰 用掉莹焰
				if Aphelios.arms1 ~= "phq" then 
					player:castSpell("self",1)
					--print("断魄+莹焰 用掉莹焰")
					return true
				end
			elseif (Aphelios.arms1 == "phq" and Aphelios.arms2 == "flr") or (Aphelios.arms1 == "flr" and Aphelios.arms2 == "phq") then  --折镜+莹焰 用掉莹焰
				if Aphelios.phqsar and Aphelios.phqsar <= 10 then
					return
				end
				if Aphelios.arms1 ~= "phq" then 
					
					player:castSpell("self",1)
					--print("折镜+莹焰 用掉莹焰")
					return true
				else
					Aphelios.phqsar = player.sar
				end
			elseif (Aphelios.arms1 == "jjq" and Aphelios.arms2 == "flr") or (Aphelios.arms1 == "flr" and Aphelios.arms2 == "jjq") then  --折镜+通碧 用掉折镜
				
				if Aphelios.arms1 ~= "flr" then 
					player:castSpell("self",1)
					--print("折镜+通碧 用掉折镜")
					return true
				end
			end
		end
	end
	local GetZlqCount = function()
		local count = 0
		for i=0, objManager.enemies_n-1 do
			local enemies = objManager.enemies[i]
			if enemies and AsUtility.isTarget(enemies) and enemies.buff['apheliosgravitumdebuff'] then
				count = count + 1
			end
		end
		return count
	end
	function Aphelios:can_zlp()
		for i=0, objManager.enemies_n-1 do
			local enemies = objManager.enemies[i]
			if enemies and AsUtility.isTarget(enemies) and enemies.buff['apheliosgravitumdebuff'] then
				return true
			end
		end
		return false
	end

	-- 执行延迟闪现
	function Aphelios:performDelayedFlash()
		if Aphelios.flashTimerActive and game.time >= Aphelios.flashScheduledTime then
			if Aphelios.savedTarget and AsUtility.isTarget(Aphelios.savedTarget) then
				local flashSlot = get_flash_slot()
				if flashSlot and player:spellSlot(flashSlot).state == 0 then
					-- 计算闪现位置
					local direction = (Aphelios.savedTarget.pos - player.pos):norm()
					local flash_pos = player.pos + direction * (player.pos:dist(Aphelios.savedTarget.pos) - JJQQ.range + 100)

					if not navmesh.isWall(flash_pos) then
						player:castSpell("pos", flashSlot, flash_pos)
					end
				end
			end

			-- 重置状态
			Aphelios.flashTimerActive = false
			Aphelios.savedTarget = nil
			return
		end
	end

	-- 通碧Q闪主逻辑
	function Aphelios:performQFlash()
		-- 检查是否需要执行延迟闪现
		Aphelios:performDelayedFlash()

		if Aphelios.qFlashActive then
			-- 按住快捷键时让角色跟随鼠标移动
			if Aphelios.menu.q.jjq.qflash:get() then
				player:move(mousePos)
			end

			-- 检查是否可以执行Q闪
			if game.time >= Aphelios.qFlashStartTime + 0.1 and AsUtility.isq() then
				local target = Aphelios:getTarget(Aphelios.menu.q.jjq.qflash_range:get())

				if target and AsUtility.isTarget(target) then
					local dist = player.pos:dist(target.pos)
					local jjq_range = JJQQ.range
					local flash_range = 400

					-- 检查是否在Q闪范围内
					if dist > jjq_range + target.boundingRadius and dist <= jjq_range + flash_range + target.boundingRadius then
						-- 检查血量和安全限制
						if AsUtility.GetPercentHealth(player) >= Aphelios.menu.q.jjq.qflash_hp:get() and
						   not AsUtility.UnderTurret(player.pos) then

							local seg = preds.linear.get_prediction(JJQQ, target)
							if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ, seg, target) then
								-- 先释放Q技能
								player:castSpell("pos", 0, seg.endPos:to3D(mousePos.y))

								-- 保存目标并设置延迟闪现
								Aphelios.savedTarget = target
								Aphelios.flashTimerActive = true
								Aphelios.flashScheduledTime = game.time + 0.25

								Aphelios.qFlashActive = false
								orb.core.set_server_pause()
								return
							end
						end
					-- 如果目标在Q技能范围内，直接释放Q技能
					elseif dist <= jjq_range + target.boundingRadius then
						local seg = preds.linear.get_prediction(JJQQ, target)
						if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ, seg, target) then
							player:castSpell("pos", 0, seg.endPos:to3D(mousePos.y))
							Aphelios.qFlashActive = false
							orb.core.set_server_pause()
							return
						end
					end
				end

				-- 超时或没有有效目标时退出Q闪模式
				if not AsUtility.isTarget(target) or game.time > Aphelios.qFlashStartTime + 3.0 then
					Aphelios.qFlashActive = false
				end
			end
		end
	end

	-- 通碧Q闪入口函数
	function Aphelios:jjq_flash()
		-- 检查Q闪状态
		if Aphelios.menu.q.jjq.qflash:get() and not Aphelios.qFlashActive then
			-- 检查基本条件
			if Aphelios.arms1 == "jjq" and can_flash() then
				Aphelios.qFlashActive = true
				Aphelios.qFlashStartTime = game.time
			end
		elseif not Aphelios.menu.q.jjq.qflash:get() and Aphelios.qFlashActive then
			Aphelios.qFlashActive = false
		end

		if Aphelios.qFlashActive then
			Aphelios:performQFlash()
			return true -- 返回true表示正在Q闪模式中
		end

		return false
	end
	
	-- 添加一个辅助函数来获取目标
	function Aphelios:getTarget(range, filter)
		-- 尝试使用内部TS模块
		local result = nil
		
		if TS then
			local function ts_filter(res, obj, dist)
				if dist > range then return false end
				
				if filter then
					if filter(obj) then
						res.target = obj
						return true
					end
				else
					res.target = obj
					return true
				end
				return false
			end
			
			result = TS.get_result(ts_filter)
			if result and result.target then
				return result.target
			end
		end
		
		-- 如果内部TS模块不可用或没有合适的目标，手动查找
		local bestTarget = nil
		local bestDistance = math.huge
		
		for i=0, objManager.enemies_n-1 do
			local enemy = objManager.enemies[i]
			if enemy and enemy.isVisible and enemy.isTargetable and not enemy.isDead and enemy.pos:dist(player.pos) <= range then
				if filter then
					if filter(enemy) and enemy.pos:dist(player.pos) < bestDistance then
						bestTarget = enemy
						bestDistance = enemy.pos:dist(player.pos)
					end
				else
					if enemy.pos:dist(player.pos) < bestDistance then
						bestTarget = enemy
						bestDistance = enemy.pos:dist(player.pos)
					end
				end
			end
		end
		
		return bestTarget
	end
	
	function Aphelios:haras()
		if not Aphelios.menu.keys.harasskey:get() then return end
		if orb.combat.target and Aphelios.next_time < 0.15 then return end
		if evade and evade.core.is_active() then return end
		if Aphelios.arms2 == "jjq" and AsUtility.isw() and Aphelios.guncontrol_time < os.clock() and Aphelios.menu.w.combo:get() and not orb.combat.target and orb.core.can_action() then
			local target = self:getTarget(750 + player.boundingRadius)
			if target then
				player:castSpell("self",1)
				sadebug("964")
				return true
			end
		end
	end
	
	function Aphelios:combat()
		
		if not Aphelios.menu.keys.combokey:get() then return end
		if orb.combat.target and Aphelios.next_time < 0.15 then return end
		if evade and evade.core.is_active() then return end
		if orb.core.can_action() then
			if orb.combat.target and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target) and AsUtility.isr() and Aphelios.menu.r.kill:get() and orb.farm.predict_hp(orb.combat.target, 1) > AsUtility.CalculateAADamage(orb.combat.target)*2 then
				local Can_Use = true
				if Aphelios.arms1  == "jjq" then
					if not Aphelios.menu.r.jjqmenu.jjqaacombo:get() and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target) then
						Can_Use = false
					end
					if Aphelios.menu.r.jjqmenu.list["on" .. orb.combat.target.charName] and Aphelios.menu.r.jjqmenu.list["on" .. orb.combat.target.charName]:get() then
					
					else
						Can_Use = false
					end
				elseif Aphelios.arms1 == "flr" then
					if not Aphelios.menu.r.flrcombo:get() and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target) then
						Can_Use = false
					end
				end
				if Can_Use then
					local rdamage = R.damage(orb.combat.target,Aphelios.arms1)
					if orb.combat.target.pos:dist(player.pos) <= Q.range and Aphelios:next_is_action() then
						if Q.speed ~= math.huge then
							local seg = preds.linear.get_prediction(Q,orb.combat.target)
							if seg and not preds.collision.get_prediction(Q,seg,orb.combat.target) then
								rdamage = rdamage+Q.damage(orb.combat.target)
							end
						else
							rdamage = rdamage+Q.damage(orb.combat.target)
						end
					end
					if rdamage > orb.farm.predict_hp(orb.combat.target, 0.5) then
						local seg = preds.linear.get_prediction(R,orb.combat.target)
						if seg and not preds.collision.get_prediction(R,seg,orb.combat.target) then
							player:castSpell("pos",3,seg.endPos:to3D(mousePos.y))
							orb.core.set_server_pause()
							print("cast7")
							return true
						end
					end
				end
				
				
				
			end
			
			if player.buff['aphelioscrescendumorbitmanager'] and Aphelios.guncontrol_time < os.clock() and player.buff['aphelioscrescendumorbitmanager'].stacks2 >= Aphelios.menu.q.flr.use:get() and Aphelios.arms2 == "flr" and AsUtility.isw() and Aphelios.menu.w.combo:get() then
				player:castSpell("self",1)
				sadebug("1020")
				return true
			end
			
			
			if  orb.combat.target  then
				 if Aphelios.arms1 == "flr" then
					if Aphelios.menu.r.flrcombo:get() and AsUtility.isr() and orb.combat.target and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target) and Aphelios.flrsar > Aphelios.menu.q.flr.saves:get() and orb.farm.predict_hp(orb.combat.target, 1) > AsUtility.CalculateAADamage(orb.combat.target)*2 and orb.combat.target.pos:dist(player) < 430 then
						if AsUtility.isTarget(orb.combat.target,true) then
							local seg = preds.linear.get_prediction(R,orb.combat.target)
							if seg then
								player:castSpell("pos",3,seg.endPos:to3D(mousePos.y))
								print("cast8")
								orb.core.set_server_pause()
								if not player.buff['aphelioscrescendumorbitmanager'] then
									player.buff['aphelioscrescendumorbitmanager'] = {stacks2 = 6}
								end
								return true
							end
						end
					end
					if orb.combat.target and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target)  and Aphelios.arms2 == "jjq" and AsUtility.isq() then  -- 搭配狙击枪使用
						local pos = player.pos + ((orb.combat.target.pos - player.pos):norm() * (orb.combat.target.pos:dist(player.pos) + 120 ))
						player:castSpell("pos",0,pos)
						orb.core.set_server_pause()
						return true
					end
					if player.buff['aphelioscrescendumorbitmanager'] and player.buff['aphelioscrescendumorbitmanager'].stacks2 >= Aphelios.menu.q.flr.use:get()  then
						return true
					end
					if orb.combat.target then
						local checkaa = Aphelios.menu.guncontrol.checkaa:get()
						local checkDag =  (AsUtility.GetOrbAADageme(player,orb.combat.target))*checkaa
						if checkDag > orb.combat.target.health then
							return
						end
					end
					local next_is_action,arms = Aphelios:next_is_action()
					if AsUtility.isw() and Aphelios.menu.w.combo:get()  and Aphelios.guncontrol_time < os.clock()  and next_is_action and arms ~= "zlp" and arms ~= "jjq" and orb.core.can_action() then
						player:castSpell("self",1)
						sadebug("1060")
						return true
					end
					return
				 end
				 if Aphelios.arms2 == "flr" and not AsUtility.isq()  and Aphelios.guncontrol_time < os.clock()  and AsUtility.isw() and Aphelios.menu.w.combo:get() then
					player:castSpell("self",1)
					sadebug("1065")
					return true
				 end
				 if orb.combat.target then
					local checkaa = Aphelios.menu.guncontrol.checkaa:get()
					local checkDag =  (AsUtility.GetOrbAADageme(player,orb.combat.target))*checkaa
					if checkDag > orb.combat.target.health then
						return
					end
				end
			end
			
			
			
			
			if Aphelios.arms1 == "flr" then
				if AsUtility.isq() and Aphelios.menu.q.flr.combo:get()  and (Aphelios.flrsar > Aphelios.menu.q.flr.saves:get() or player.sar <= 2)  then
					if orb.combat.target and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target)  and Aphelios.arms2 == "jjq" then  -- 搭配狙击枪使用
						player:castSpell("pos",0,orb.combat.target.pos)
						orb.core.set_server_pause()
						return true
					end
					if orb.combat.target and orb.combat.target.pos:dist(player.pos) <= AsUtility.GetAARange(orb.combat.target)  and player.sar <= 2 then
						player:castSpell("pos",0,orb.combat.target.pos)
						orb.core.set_server_pause()
						return true
					end
				end
				
				if player.buff['aphelioscrescendumorbitmanager'] and player.buff['aphelioscrescendumorbitmanager'].stacks2 >= Aphelios.menu.q.flr.use:get()  then
					return true
				end
				if orb.combat.target then
					local checkaa = Aphelios.menu.guncontrol.checkaa:get()
					local checkDag =  (AsUtility.GetOrbAADageme(player,orb.combat.target))*checkaa
					if checkDag > orb.combat.target.health then
						return
					end
				end
				local next_is_action,arms,range = Aphelios:next_is_action()
				if AsUtility.isw() and Aphelios.menu.w.combo:get() and range > 0   and Aphelios.guncontrol_time < os.clock()  and next_is_action and arms ~= "zlp" and arms ~= "jjq" and orb.core.can_action()  and (Aphelios.next_time > 0.1 or not orb.combat.target) then
					local target = self:getTarget(range)
					if target then
						player:castSpell("self",1)
						sadebug("1107")
						return true
					end
				end
				
			end
			if Aphelios.arms2 == "zlp" and AsUtility.isw()  and Aphelios.guncontrol_time < os.clock()  and Aphelios.menu.w.combo:get() and orb.combat.target and orb.core.can_action()   then
				local target = self:getTarget(550 + player.boundingRadius)
				if target then
					local next_is_action,arms = Aphelios:next_is_action()
					if next_is_action then
						player:castSpell("self",1)
						sadebug("1118")
						return true
					end
				end
			end
			
			if Aphelios.arms2 == "jjq" and AsUtility.isw()  and Aphelios.guncontrol_time < os.clock()  and Aphelios.menu.w.combo:get() and not orb.combat.target and orb.core.can_action()   then
				local target = self:getTarget(750 + player.boundingRadius)
				if target then
					player:castSpell("self",1)
					sadebug("1128")
					return true
				end
				
				if Aphelios:next_is_action() and Aphelios.menu.q.jjq.combo:get() and (Aphelios.jjqsar > Aphelios.menu.q.jjq.saves:get() or Aphelios.jjqsar < 2)   then
					local target = self:getTarget(JJQQ.range, function(t)
						if not t.buff['aphelioscalibrumbonusrangedebuff'] then
							local seg = preds.linear.get_prediction(JJQQ,t)
							if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,t) then
								return true
							end
						end
						return false
					end)
					
					if target and AsUtility.isTarget(target,true) then
						local seg = preds.linear.get_prediction(JJQQ,target)
						if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,target) and (Aphelios:trace_filter(JJQQ,seg,target) or AsUtility.iskq()) then
							player:castSpell("self",1)
							return true
						end
					end
				end
				
			end
			if Aphelios.arms1 == "zlp" and Aphelios.menu.q.zlp.combo:get()  then
				if AsUtility.isq() and orb.combat.target  then
					return true
				end
				if AsUtility.isq() and Aphelios:can_zlp()  then
					player:castSpell("self",0)
					orb.core.set_server_pause()
					return true
				end
				if AsUtility.isw() and Aphelios.menu.w.combo:get()  and Aphelios.guncontrol_time < os.clock()  and (Aphelios.next_time > 0.1 or not orb.combat.target)  then
					local next_is_action,arms = Aphelios:next_is_action()
					if next_is_action and arms == "phq" then
						
						local target = self:getTarget(PHQQ.range)
						if target and AsUtility.isTarget(target,true) and target.pos:dist(player.pos) < PHQQ.range  then
							local seg = preds.linear.get_prediction(PHQQ,target)
							if seg and seg:length() < PHQQ.range and (Aphelios:trace_filter(PHQQ,seg,target) or AsUtility.iskq()) then
								player:castSpell("self",1)
								sadebug("1168")
								return true
							end
						end
					end
				end
			end
			
			if Aphelios.arms2 == "zlp" and Aphelios.menu.q.zlp.combo:get()   then
				if AsUtility.isw() and Aphelios.menu.w.combo:get()  and Aphelios.guncontrol_time < os.clock()  and Aphelios:can_zlp() and Aphelios:next_is_action() then
					player:castSpell("self",1)
					sadebug("1180")
					return true
				end
				
			end
			
			if Aphelios.arms1 == "sq" then
				if AsUtility.isq() and Aphelios.menu.q.sq.combo:get() then
					local target = self:getTarget(SQQ.range+player.boundingRadius+80)
					if target and AsUtility.isTarget(target,true) then
						player:castSpell("self",0)
						orb.core.set_server_pause()
						player:move(mousePos)
						return true
					end
				end
				
				if Aphelios.arms2 == "jjq" and AsUtility.isw() and Aphelios.menu.w.combo:get() and Aphelios.guncontrol_time < os.clock() then
					if Aphelios:next_is_action() and Aphelios.menu.q.jjq.combo:get() and (Aphelios.jjqsar > Aphelios.menu.q.jjq.saves:get() or Aphelios.jjqsar < 2) then
						local target = self:getTarget(JJQQ.range, function(t)
							if not t.buff['aphelioscalibrumbonusrangedebuff'] then
								local seg = preds.linear.get_prediction(JJQQ,t)
								if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,t) then
									return true
								end
							end
							return false
						end)
						
						if target and AsUtility.isTarget(target,true) then
							local seg = preds.linear.get_prediction(JJQQ,target)
							if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,target) and (Aphelios:trace_filter(JJQQ,seg,target) or AsUtility.iskq()) then
								player:castSpell("self",1)
								sadebug("1210")
								return true
							end
						end
					end
				end
				
				
			end
			
			if Aphelios.arms1 == "jjq" then
				if AsUtility.isq() and Aphelios.menu.q.jjq.combo:get() and (Aphelios.jjqsar > Aphelios.menu.q.jjq.saves:get() or Aphelios.jjqsar < 2) then
					local target = self:getTarget(JJQQ.range, function(t)
						if not t.buff['aphelioscalibrumbonusrangedebuff'] then
							local seg = preds.linear.get_prediction(JJQQ,t)
							if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,t) then
								return true
							end
						end
						return false
					end)
					
					if target and AsUtility.isTarget(target,true) then
						local seg = preds.linear.get_prediction(JJQQ,target)
						if seg and seg:length() < JJQQ.range and not preds.collision.get_prediction(JJQQ,seg,target) and (Aphelios:trace_filter(JJQQ,seg,target) or AsUtility.iskq()) then
							player:castSpell("pos",0,seg.endPos:to3D(mousePos.y))
							orb.core.set_server_pause()
							return true
						end
					end
					
					local ta = self:getTarget(JJQQ.range)
					if ta then return end
				end
				
				
				local target_2 = self:getTarget(550 + player.boundingRadius)
				if not target_2 and orb.combat.target then
					return true
				end
				
			end
			
			if Aphelios.arms1 == "phq" then
				if AsUtility.isq() and Aphelios.menu.q.phq.combo:get() then
					local target = self:getTarget(PHQQ.range)
					if target and AsUtility.isTarget(target,true) then
						local seg = preds.linear.get_prediction(PHQQ,target)
						if seg and seg:length() < PHQQ.range and (Aphelios:trace_filter(PHQQ,seg,target) or AsUtility.iskq()) then
							player:castSpell("pos",0,seg.endPos:to3D(mousePos.y))
							orb.core.set_server_pause()
							return true
						end
					end
				end
				
				local next_is_action,arms = Aphelios:next_is_action()
				if AsUtility.isw() and Aphelios.menu.w.combo:get() and Aphelios.guncontrol_time < os.clock() and next_is_action and (Aphelios.next_time > 0.1 or not orb.combat.target) then
					if arms == "sq" then
						local target = self:getTarget(SQQ.range+player.boundingRadius+20)
						if target and AsUtility.isTarget(target,true) then
							player:castSpell("self",1)
							sadebug("1268")
							return true
						end
					end
				end
			end
			
			
			
			
			Aphelios:guncontrol(true)
			
		end
	end
	
	function Aphelios:manual()
		
		if Aphelios.menu.r.manual:get() and AsUtility.isr() then
			if orb.core.can_action() then
				player:move(player.pos + ((mousePos - player.pos):norm() * 300))
				local mode = Aphelios.menu.r.mode:get()
				local target = self:getTarget(R.range)
				if target and AsUtility.isTarget(target) then
					local predsPos = preds.linear.get_prediction(R, target)
					if predsPos and predsPos.startPos:dist(predsPos.endPos) <= R.range and (Aphelios:trace_filter(R,predsPos,target) or AsUtility.iskr()) then
						if not AsUtility.YasuoWall_check(player.pos,vec3(predsPos.endPos.x, mousePos.y, predsPos.endPos.y)) then
							
							player:castSpell("pos", 3, vec3(predsPos.endPos.x, mousePos.y, predsPos.endPos.y))
							print("cast9")
							orb.core.set_server_pause()
							return
						end
					end
				end
			end
		end
		
		
	end
	
	function Aphelios:misc()
	
	
	end
	
	function Aphelios:zlpkey()
		if Aphelios.menu.q.zlp.zlpkey:get() then
			if Aphelios.arms2 == "zlp" and AsUtility.isw() and Aphelios:can_zlp() then
				local next_is_action,arms = Aphelios:next_is_action()
				if next_is_action then
					player:castSpell("self",1)
					player:castSpell("self",0)
				end
			elseif Aphelios.arms1 == "zlp" and AsUtility.isq() and Aphelios:can_zlp() then
				player:castSpell("self",0)
			end
		end
	end
	
	function Aphelios.on_tick()
		Aphelios:init()

		-- 检查通碧Q闪（优先级最高）
		if Aphelios:jjq_flash() then
			return -- 如果在Q闪模式中，跳过其他逻辑
		end

		Aphelios:loincR()
		Aphelios:misc()
		Aphelios:combat()
		Aphelios:haras()
		Aphelios:manual()
		Aphelios:guncontrol()
		Aphelios:zlpkey()
	end
	
	function Aphelios.on_process_spell(spell)
	
		if spell and spell.owner and spell.owner == player then
			
			
			if spell.name == "ApheliosInfernumQ" then
				orb.core.set_pause(spell.windUpTime)
			end
			if spell.name == "ApheliosCalibrumAttackOverride" then
				Aphelios.ApheliosCalibrumAttackOverride =  os.clock() + player:basicAttack(0).windUpTime + network.latency + spell.windUpTime
			end
			if spell.name == "ApheliosGravitumAttack" and spell.target and spell.target.type == TYPE_HERO and ZLPQ.slot.name == 'ApheliosGravitumQ' then
				Aphelios.ZLPQTARGET = os.clock() + 0.5
			end
			
			-- 记录攻击时间
			if spell.name:lower():find("attack") then
				Aphelios.attack_time = os.clock()
			end
		end
	end
	
	function Aphelios.on_draw()
	
		if player.isOnScreen then
			if Aphelios.menu.guncontrol.open:get() and Aphelios.guncontrol_time > os.clock() then
				if chinese then
					graphics.draw_text_2D("禁止切换武器 " .. tostring(AsUtility.GetPreciseDecimal(Aphelios.guncontrol_time - os.clock(),1).."秒" ) , 20, graphics.width/2.3, graphics.height/1.3, graphics.argb(255, 255,128,30	 ))
				else
					graphics.draw_text_2D("Close W " .. tostring(AsUtility.GetPreciseDecimal(Aphelios.guncontrol_time - os.clock(),1).."second" ) , 20, graphics.width/2.3, graphics.height/1.3, graphics.argb(255, 255,128,30	 ))
				end
			end 
		end
	end
	
	function Aphelios.AntiGapcloser_func(spell)
		
	end
	
	if player.charName ~= "Viego" then
		cb.add(cb.draw, Aphelios.on_draw)
		cb.add(cb.spell, Aphelios.on_process_spell)
		orb.combat.register_f_pre_tick(Aphelios.on_tick)
		--AntiGapcloser.register_f(Aphelios.AntiGapcloser_func)
	end
end


return Aphelios