#include <windows.h>
#include <atomic>
#include <cmath>

// 全局变量
std::atomic<size_t> g_ticks(0);
void* g_original_function = nullptr;
std::atomic<bool> g_control_enabled(true);
std::atomic<int> g_control_mode(1); // 0=自动, 1=鼠标, 2=手动

// 位置数据
struct Position {
    float x, y, z;
};

Position g_mouse_pos = {0, 0, 0};
Position g_player_pos = {0, 0, 0};
Position g_target_pos = {0, 0, 0};

// 控制参数
const int UPDATE_FREQUENCY = 3;
const float MAX_RANGE = 2500.0f;
const float PREDICTION_TIME = 0.25f;

// 计算距离
float distance(const Position& a, const Position& b) {
    float dx = a.x - b.x;
    float dy = a.y - b.y;
    float dz = a.z - b.z;
    return sqrt(dx*dx + dy*dy + dz*dz);
}

// 归一化向量
Position normalize(const Position& vec) {
    float len = sqrt(vec.x*vec.x + vec.y*vec.y + vec.z*vec.z);
    if (len > 0.001f) {
        return {vec.x/len, vec.y/len, vec.z/len};
    }
    return {0, 0, 0};
}

// 计算鼠标方向
Position calculate_mouse_direction(const float coords[3]) {
    if (distance(g_mouse_pos, g_player_pos) < 100.0f) {
        return {coords[0], coords[1], coords[2]};
    }
    
    // 计算从玩家到鼠标的方向
    Position direction = {
        g_mouse_pos.x - g_player_pos.x,
        g_mouse_pos.y - g_player_pos.y,
        g_mouse_pos.z - g_player_pos.z
    };
    
    direction = normalize(direction);
    
    // 在该方向上延伸
    float extend_distance = 2000.0f;
    return {
        g_player_pos.x + direction.x * extend_distance,
        coords[1], // 保持Y坐标
        g_player_pos.z + direction.z * extend_distance
    };
}

// 计算自动追踪
Position calculate_auto_tracking(const float coords[3]) {
    if (distance(g_target_pos, {0, 0, 0}) < 10.0f) {
        return {coords[0], coords[1], coords[2]};
    }
    
    // 预测目标位置
    return {
        g_target_pos.x + PREDICTION_TIME * 350.0f, // 假设移动速度
        coords[1],
        g_target_pos.z + PREDICTION_TIME * 350.0f
    };
}

// 主Hook函数
extern "C" __declspec(dllexport) void __fastcall on_steer(
    size_t a1, size_t a2, unsigned char slot, float coords[3], unsigned char finish) {
    
    // 增加tick计数
    g_ticks.fetch_add(1);
    
    // 只对塞恩的R技能（slot 3）生效
    if (slot == 3 && g_control_enabled.load()) {
        size_t tick = g_ticks.load();
        
        // 每N个tick更新一次
        if (tick % UPDATE_FREQUENCY == 0) {
            Position new_pos;
            
            switch (g_control_mode.load()) {
                case 1: // 鼠标控制
                    new_pos = calculate_mouse_direction(coords);
                    break;
                case 0: // 自动追踪
                    new_pos = calculate_auto_tracking(coords);
                    break;
                default: // 手动控制或其他
                    new_pos = {coords[0], coords[1], coords[2]};
                    break;
            }
            
            // 应用新坐标
            coords[0] = new_pos.x;
            coords[1] = new_pos.y;
            coords[2] = new_pos.z;
        }
    }
    
    // 调用原函数
    if (g_original_function) {
        typedef void(__fastcall* OriginalFunc)(size_t, size_t, unsigned char, float[3], unsigned char);
        ((OriginalFunc)g_original_function)(a1, a2, slot, coords, finish);
    }
}

// 导出函数
extern "C" __declspec(dllexport) void init_sion_hook(void* original_function) {
    g_original_function = original_function;
    g_control_enabled.store(true);
    g_control_mode.store(1); // 默认鼠标控制
}

extern "C" __declspec(dllexport) void set_control_enabled(bool enabled) {
    g_control_enabled.store(enabled);
}

extern "C" __declspec(dllexport) void set_control_mode(int mode) {
    g_control_mode.store(mode);
}

extern "C" __declspec(dllexport) void update_mouse_position(float x, float y, float z) {
    g_mouse_pos = {x, y, z};
}

extern "C" __declspec(dllexport) void update_player_position(float x, float y, float z) {
    g_player_pos = {x, y, z};
}

extern "C" __declspec(dllexport) void set_target_enemy_position(float x, float y, float z) {
    g_target_pos = {x, y, z};
}

extern "C" __declspec(dllexport) size_t get_tick_count() {
    return g_ticks.load();
}

extern "C" __declspec(dllexport) void reset_tick_count() {
    g_ticks.store(0);
}

extern "C" __declspec(dllexport) int get_control_status() {
    if (g_control_enabled.load()) {
        return g_control_mode.load();
    }
    return -1; // 禁用状态
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            // 初始化
            break;
        case DLL_THREAD_ATTACH:
        case DLL_THREAD_DETACH:
        case DLL_PROCESS_DETACH:
            break;
    }
    return TRUE;
}
