@echo off
echo 编译简化版Hook...

REM 检查Rust
where rustc >nul 2>nul
if %errorlevel% neq 0 (
    echo 请先安装Rust: https://rustup.rs/
    pause
    exit /b 1
)

REM 清理
if exist "simple_hook" rmdir /s /q simple_hook

REM 创建项目
cargo new simple_hook --lib

REM 配置Cargo.toml
echo [package] > simple_hook\Cargo.toml
echo name = "simple_hook" >> simple_hook\Cargo.toml
echo version = "0.1.0" >> simple_hook\Cargo.toml
echo edition = "2021" >> simple_hook\Cargo.toml
echo. >> simple_hook\Cargo.toml
echo [lib] >> simple_hook\Cargo.toml
echo crate-type = ["cdylib"] >> simple_hook\Cargo.toml

REM 复制代码
copy simple_hook.rs simple_hook\src\lib.rs

REM 编译
cd simple_hook
cargo build --release

if %errorlevel% equ 0 (
    echo 编译成功！
    echo DLL位置: target\release\simple_hook.dll
    dir target\release\simple_hook.dll
) else (
    echo 编译失败
)

cd ..
pause
