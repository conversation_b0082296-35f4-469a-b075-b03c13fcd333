@echo off
chcp 65001 >nul
echo 编译C++版本塞恩Hook...

REM 检查是否有Visual Studio
where cl >nul 2>nul
if %errorlevel% equ 0 (
    echo 找到Visual Studio编译器，使用MSVC编译...
    goto :compile_msvc
)

REM 检查是否有MinGW
where g++ >nul 2>nul
if %errorlevel% equ 0 (
    echo 找到MinGW编译器，使用GCC编译...
    goto :compile_gcc
)

REM 都没有找到
echo 错误: 未找到C++编译器
echo.
echo 请安装以下任一编译器:
echo 1. Visual Studio (推荐)
echo    下载地址: https://visualstudio.microsoft.com/
echo 2. MinGW-w64
echo    下载地址: https://www.mingw-w64.org/
echo 3. TDM-GCC
echo    下载地址: https://jmeubank.github.io/tdm-gcc/
echo.
pause
exit /b 1

:compile_msvc
echo 使用MSVC编译...
cl /LD /O2 /std:c++11 sion_hook.cpp /Fe:sion_hook.dll
if %errorlevel% equ 0 (
    echo ✅ MSVC编译成功！
    goto :success
) else (
    echo ❌ MSVC编译失败
    goto :failed
)

:compile_gcc
echo 使用GCC编译...
g++ -shared -O2 -std=c++11 -o sion_hook.dll sion_hook.cpp
if %errorlevel% equ 0 (
    echo ✅ GCC编译成功！
    goto :success
) else (
    echo ❌ GCC编译失败
    goto :failed
)

:success
echo.
echo 📁 输出文件: sion_hook.dll
if exist sion_hook.dll (
    echo 📏 文件大小:
    dir sion_hook.dll | find "sion_hook.dll"
) else (
    echo ⚠️  警告: DLL文件未找到
)
echo.
echo 🎯 Hook函数列表:
echo - init_sion_hook(addr)
echo - set_control_enabled(bool)
echo - set_control_mode(0-2)
echo - update_mouse_position(x,y,z)
echo - update_player_position(x,y,z)
echo - set_target_enemy_position(x,y,z)
echo.
echo 📋 使用说明:
echo 1. 将 sion_hook.dll 注入到游戏进程
echo 2. 调用 init_sion_hook(原函数地址) 初始化
echo 3. 使用 set_control_enabled(true) 启用控制
echo 4. 设置控制模式: 0=自动追踪, 1=鼠标控制, 2=手动
echo.
goto :end

:failed
echo.
echo 常见问题解决:
echo 1. 确保编译器已正确安装并添加到PATH
echo 2. 检查源代码是否有语法错误
echo 3. 确保有足够的磁盘空间和权限
echo 4. 尝试以管理员身份运行
echo.

:end
echo 按任意键退出...
pause >nul
