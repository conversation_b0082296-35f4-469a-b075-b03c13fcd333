# 塞恩大招控制系统 - 安装和编译指南

## 问题诊断

看起来你的系统中没有安装Rust编译器，这就是为什么无法生成DLL文件的原因。

## 解决方案

### 方案1: 安装Rust并编译 (推荐)

#### 步骤1: 安装Rust
1. 访问 https://rustup.rs/
2. 下载并运行 `rustup-init.exe`
3. 按照提示完成安装
4. 重启命令提示符

#### 步骤2: 验证安装
```cmd
rustc --version
cargo --version
```

#### 步骤3: 编译Hook
```cmd
# 运行编译脚本
build_simple.bat
```

### 方案2: 使用预编译版本

如果不想安装Rust，我可以为你提供一个C++版本的Hook，更容易编译。

## C++版本Hook (无需Rust)

让我为你创建一个C++版本，可以用Visual Studio或MinGW编译：

```cpp
// sion_hook.cpp
#include <windows.h>
#include <atomic>

// 全局变量
std::atomic<size_t> g_ticks(0);
void* g_original_function = nullptr;

// Hook函数
extern "C" __declspec(dllexport) void __fastcall on_steer(
    size_t a1, size_t a2, unsigned char slot, float coords[3], unsigned char finish) {
    
    // 增加tick计数
    g_ticks.fetch_add(1);
    
    // 只对塞恩的R技能（slot 3）生效
    if (slot == 3) {
        size_t tick = g_ticks.load();
        
        // 每5个tick修改一次方向
        if (tick % 5 == 0) {
            coords[0] = INFINITY;
            coords[1] = INFINITY;
            coords[2] = INFINITY;
        }
    }
    
    // 调用原函数
    if (g_original_function) {
        typedef void(__fastcall* OriginalFunc)(size_t, size_t, unsigned char, float[3], unsigned char);
        ((OriginalFunc)g_original_function)(a1, a2, slot, coords, finish);
    }
}

// 初始化函数
extern "C" __declspec(dllexport) void init_sion_hook(void* original_function) {
    g_original_function = original_function;
}

// 获取tick计数
extern "C" __declspec(dllexport) size_t get_tick_count() {
    return g_ticks.load();
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    return TRUE;
}
```

## 编译C++版本

### 使用Visual Studio
1. 创建新的DLL项目
2. 添加上面的代码
3. 编译为Release版本

### 使用MinGW
```cmd
g++ -shared -o sion_hook.dll sion_hook.cpp -std=c++11
```

## 方案3: 仅使用Lua脚本

如果编译Hook有困难，你也可以仅使用优化后的Lua脚本。虽然控制精度不如Hook版本，但仍然可以实现基本的追踪功能。

### Lua脚本优势
- 无需编译
- 直接修改即可使用
- 兼容性好
- 易于调试

### 使用方法
1. 确保 `Champions/Sion.lua` 已加载
2. 按L键启用追踪
3. 使用鼠标控制大招方向

## 功能对比

| 功能 | Rust Hook | C++ Hook | 仅Lua |
|------|-----------|----------|-------|
| 精确控制 | ✅ | ✅ | ⚠️ |
| 实时响应 | ✅ | ✅ | ⚠️ |
| 易于安装 | ❌ | ⚠️ | ✅ |
| 兼容性 | ⚠️ | ✅ | ✅ |
| 检测风险 | ⚠️ | ⚠️ | ✅ |

## 推荐使用顺序

1. **首选**: 安装Rust并使用Rust Hook (最强功能)
2. **备选**: 使用C++ Hook (平衡选择)
3. **保底**: 仅使用Lua脚本 (最安全)

## 故障排除

### 编译失败
- 确保已安装编译器
- 检查代码语法
- 确认系统架构匹配

### Hook不生效
- 确认DLL已正确注入
- 检查函数地址是否正确
- 验证游戏版本兼容性

### 游戏崩溃
- 检查Hook函数签名
- 确认内存访问安全
- 使用调试版本排查

## 联系支持

如果遇到问题，请提供：
1. 操作系统版本
2. 编译器版本
3. 错误信息截图
4. 游戏版本信息

建议先尝试C++版本，它更容易编译且兼容性更好。
