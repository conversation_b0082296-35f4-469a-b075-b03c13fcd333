// 简化版塞恩大招控制Hook
use std::sync::atomic::{AtomicUsize, Ordering};

// 全局变量
static TICKS: AtomicUsize = AtomicUsize::new(0);
static mut STEER_ORG: usize = 0;

// 主要的 hook 函数
#[no_mangle]
pub extern "fastcall" fn on_steer(a1: usize, a2: usize, slot: u8, coords: &mut [f32; 3], finish: u8) {
    // 增加tick计数
    TICKS.fetch_add(1, Ordering::Relaxed);
    
    // 只对塞恩的R技能（slot 3）生效
    if slot == 3 {
        let tick = TICKS.load(Ordering::Relaxed);
        
        // 每5个tick修改一次方向，实现简单的"抖动"效果
        if tick % 5 == 0 {
            // 设置坐标为无穷大，让游戏重新计算方向
            coords[0] = f32::INFINITY;
            coords[1] = f32::INFINITY;
            coords[2] = f32::INFINITY;
        }
    }
    
    // 调用原函数
    unsafe {
        if STEER_ORG != 0 {
            let org_fn: extern "fastcall" fn(usize, usize, u8, &mut [f32; 3], u8) =
                std::mem::transmute(STEER_ORG);
            org_fn(a1, a2, slot, coords, finish);
        }
    }
}

// 初始化 hook
#[no_mangle]
pub extern "C" fn init_sion_hook(original_function: usize) {
    unsafe {
        STEER_ORG = original_function;
    }
}

// 获取当前tick计数
#[no_mangle]
pub extern "C" fn get_tick_count() -> usize {
    TICKS.load(Ordering::Relaxed)
}

// 重置tick计数
#[no_mangle]
pub extern "C" fn reset_tick_count() {
    TICKS.store(0, Ordering::Relaxed);
}
